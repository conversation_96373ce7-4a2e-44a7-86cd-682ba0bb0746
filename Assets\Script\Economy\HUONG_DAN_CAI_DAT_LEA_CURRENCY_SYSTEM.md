# 💰 Hướng Dẫn Cài Đặt Hệ Thống Tiền Tệ Lea & Kho Hàng

## 📋 Tổng Quan

Hệ thống này bao gồm:
- **💰 Hệ thống tiền tệ Lea** với auto-save và visual effects
- **🎒 Kho hàng người chơi** với 50 slots và tìm kiếm
- **🎨 Giao diện UI** hiển thị tiền và kho hàng
- **🔧 Inspector fields** có thể điều chỉnh trong Edit mode

### ✨ Tính Năng Chính

- 💰 **Quản lý tiền Lea** với validation và events
- 🎒 **Kho hàng thông minh** với auto-sort và search
- 🎨 **UI animations** khi thay đổi tiền/vật phẩm
- 💾 **Auto-save** dữ liệu định kỳ
- 🔧 **Context Menu** để test và debug
- 🎯 **Tích hợp shop** mua/bán vật phẩm

---

## 🚀 Phần 1: Thiết Lập Hệ Thống Cơ Bản

### Bước 1: Tạo Economy System Manager

1. **Tạo GameObject chính:**
   ```
   GameObject → Create Empty → "EconomySystem"
   Add Component → Economy System Manager
   Add Component → Lea Currency Manager
   Add Component → Player Inventory
   ```

2. **Cấu hình EconomySystemManager:**
   ```
   System References:
   ├── Currency Manager: [Tự động tìm]
   ├── Player Inventory: [Tự động tìm]
   ├── Currency UI: [Sẽ gán sau]
   └── Inventory UI: [Sẽ gán sau]

   Cài Đặt Hệ Thống:
   ├── Tự Động Khởi Tạo: ✓
   ├── Hiển Thị Log: ✓
   └── Tự Động Tìm Components: ✓

   Cài Đặt Lưu Trữ:
   ├── Tự Động Lưu Data: ✓
   ├── Khoảng Thời Gian Lưu: 60 (giây)
   └── Tạo Backup: ✓

   Shop Integration:
   └── Phần Trăm Giảm Giá Bán: 0.5 (50%)
   ```

### Bước 2: Cấu Hình Currency Manager

1. **Cài đặt LeaCurrencyManager:**
   ```
   💰 Cài Đặt Tiền Tệ Lea:
   ├── Số Tiền Hiện Tại: 100 (Lea khởi tạo)
   ├── Số Tiền Tối Đa: 999999 (Giới hạn)
   ├── Tự Động Lưu Tiền: ✓
   └── Hiển Thị Log: ✓

   💾 Cài Đặt Lưu Trữ:
   ├── Key Lưu Trữ: "LeaCurrency"
   └── Khoảng Thời Gian Lưu: 30 (giây)

   🎨 Hiệu Ứng:
   ├── Có Hiệu Ứng: ✓
   └── Thời Gian Hiệu Ứng: 0.5 (giây)
   ```

### Bước 3: Cấu Hình Player Inventory

1. **Cài đặt PlayerInventory:**
   ```
   🎒 Cài Đặt Kho Hàng:
   ├── Số Slot Tối Đa: 50
   ├── Tự Động Sắp Xếp: ✓
   ├── Tự Động Lưu Kho: ✓
   └── Hiển Thị Log: ✓

   💾 Cài Đặt Lưu Trữ:
   ├── Key Lưu Trữ: "PlayerInventory"
   └── Khoảng Thời Gian Lưu: 60 (giây)
   ```

---

## 🎨 Phần 2: Thiết Lập Giao Diện UI

### Bước 1: Tạo Canvas UI

1. **Tạo Canvas chính:**
   ```
   GameObject → UI → Canvas
   Render Mode: Screen Space - Overlay
   Canvas Scaler: Scale With Screen Size
   Reference Resolution: 1920x1080
   ```

### Bước 2: Tạo Currency UI

1. **Tạo Currency HUD:**
   ```
   Canvas → Create Empty → "CurrencyHUD"
   Position: Top-right corner (Anchor: Top-Right)
   ```

2. **Thêm UI Elements:**
   ```
   CurrencyHUD/
   ├── Background (Image)
   │   ├── Color: (0, 0, 0, 0.5) - Nền trong suốt
   │   └── Size: (200, 60)
   ├── LeaIcon (Image)
   │   ├── Position: Left side
   │   └── Size: (40, 40)
   ├── CurrencyText (TextMeshPro)
   │   ├── Text: "100 Lea"
   │   ├── Font Size: 24
   │   ├── Color: White
   │   └── Alignment: Center
   └── CurrencySlider (Slider) [Optional]
       ├── Fill Color: Gold
       └── Background Color: Dark Gray
   ```

3. **Thêm LeaCurrencyUI Component:**
   ```
   CurrencyHUD → Add Component → Lea Currency UI

   🎨 UI References:
   ├── Text Tiền Tệ: [Gán CurrencyText]
   ├── Icon Tiền Tệ: [Gán LeaIcon]
   ├── Slider Tiền Tệ: [Gán CurrencySlider]
   └── Panel Tiền Tệ: [Gán CurrencyHUD]

   🎭 Animation Settings:
   ├── Có Animation: ✓
   ├── Thời Gian Animation: 0.5
   ├── Curve Tăng Tiền: EaseInOut
   └── Curve Giảm Tiền: EaseInOut

   🌈 Color Settings:
   ├── Màu Bình Thường: White
   ├── Màu Tăng Tiền: Green
   ├── Màu Giảm Tiền: Red
   └── Màu Không Đủ Tiền: Red

   ✨ Effects:
   ├── Có Hiệu Ứng Rung: ✓
   ├── Cường Độ Rung: 5
   ├── Hiệu Ứng Tăng Tiền: [Optional Particle]
   └── Hiệu Ứng Giảm Tiền: [Optional Particle]

   🔧 Settings:
   ├── Định Dạng Tiền: "{0:N0} Lea"
   ├── Hiển Thị Slider: ✓
   └── Hiển Thị Log: ✗
   ```

### Bước 3: Tạo Inventory UI

1. **Tạo Inventory Panel:**
   ```
   Canvas → Create Empty → "InventoryPanel"
   Position: Center screen
   Size: (800, 600)
   Active: False (Ẩn ban đầu)
   ```

2. **Thêm UI Elements:**
   ```
   InventoryPanel/
   ├── Background (Image)
   │   ├── Color: (0.1, 0.1, 0.1, 0.9)
   │   └── Size: Full
   ├── Header/
   │   ├── Title (TextMeshPro): "Kho Hàng"
   │   ├── SlotInfo (TextMeshPro): "25/50"
   │   ├── ProgressBar (Slider): Hiển thị % đầy
   │   └── CloseButton (Button): "X"
   ├── SearchPanel/
   │   ├── SearchInput (TMP_InputField): "Tìm kiếm..."
   │   ├── SearchButton (Button): "Tìm"
   │   └── ClearButton (Button): "Xóa"
   ├── ItemContainer/
   │   ├── ScrollView (Scroll Rect)
   │   └── Content (Vertical Layout Group)
   └── Controls/
       ├── SortButton (Button): "Sắp Xếp"
       └── ClearAllButton (Button): "Xóa Tất Cả"
   ```

3. **Tạo Item Slot Prefab:**
   ```
   Create Empty → "ItemSlotPrefab"
   
   ItemSlotPrefab/
   ├── Background (Image): Nền slot
   ├── ItemIcon (Image): Icon vật phẩm
   ├── ItemName (TextMeshPro): Tên vật phẩm
   ├── ItemQuantity (TextMeshPro): Số lượng
   └── SlotButton (Button): Để click

   Add Component → Inventory Slot UI
   ```

4. **Thêm InventoryUI Component:**
   ```
   InventoryPanel → Add Component → Inventory UI

   🎨 UI References:
   ├── Panel Kho Hàng: [Gán InventoryPanel]
   ├── Container Vật Phẩm: [Gán Content]
   ├── Prefab Slot Vật Phẩm: [Gán ItemSlotPrefab]
   └── Scroll Rect: [Gán ScrollView]

   📊 Thông Tin Kho:
   ├── Text Số Slot: [Gán SlotInfo]
   ├── Slider Đầy Kho: [Gán ProgressBar]
   └── Text Phần Trăm Đầy: [Optional]

   🔍 Tìm Kiếm:
   ├── Input Tìm Kiếm: [Gán SearchInput]
   ├── Button Tìm Kiếm: [Gán SearchButton]
   └── Button Xóa Tìm Kiếm: [Gán ClearButton]

   🔧 Điều Khiển:
   ├── Button Đóng Kho: [Gán CloseButton]
   ├── Button Sắp Xếp: [Gán SortButton]
   └── Button Xóa Toàn Bộ: [Gán ClearAllButton]

   ⚙️ Settings:
   ├── Tự Động Cập Nhật: ✓
   ├── Hiển Thị Log: ✗
   └── Phím Mở Kho: Tab
   ```

---

## 🔗 Phần 3: Tích Hợp Với Hệ Thống Hiện Tại

### Bước 1: Kết Nối UI với Managers

1. **Gán UI References vào EconomySystemManager:**
   ```
   EconomySystem GameObject:
   ├── Currency UI: [Gán LeaCurrencyUI component]
   └── Inventory UI: [Gán InventoryUI component]
   ```

### Bước 2: Tích Hợp Với Player System

1. **Thêm vào Player GameObject:**
   ```
   Player → Add Component → Economy Player Adapter (nếu có)
   
   Hoặc tạo reference trong PlayerController:
   ```

2. **Script tích hợp đơn giản:**
   ```csharp
   // Trong PlayerController hoặc script tương tự
   public EconomySystemManager economySystem;
   
   void Start() {
       economySystem = FindObjectOfType<EconomySystemManager>();
   }
   
   // Sử dụng
   economySystem.MuaVatPham("wood", 5, 10);
   economySystem.BanVatPham("stone", 2, 15);
   ```

---

## 🎮 Phần 4: Hướng Dẫn Sử Dụng

### Controls - Phím Điều Khiển

| Phím | Chức Năng |
|------|-----------|
| **Tab** | Mở/đóng kho hàng |
| **Esc** | Đóng UI panels |

### Cách Sử Dụng Trong Game

#### 💰 Quản Lý Tiền Lea
- **Xem số dư:** Hiển thị ở góc màn hình
- **Animation:** Tự động khi thay đổi tiền
- **Màu sắc:** Xanh khi tăng, đỏ khi giảm

#### 🎒 Quản Lý Kho Hàng
- **Mở kho:** Nhấn Tab
- **Tìm kiếm:** Gõ tên vật phẩm
- **Sắp xếp:** Nhấn nút "Sắp Xếp"
- **Xóa:** Nhấn nút "Xóa Tất Cả"

#### 🏪 Mua/Bán Vật Phẩm
```csharp
// Mua vật phẩm
economySystem.MuaVatPham("wood", 5, 10); // ID, số lượng, giá

// Bán vật phẩm  
economySystem.BanVatPham("wood", 2, 10); // ID, số lượng, giá gốc
```

---

## 🔧 Phần 5: Debug và Testing

### Context Menu Commands

#### EconomySystemManager
- **"Khởi Tạo Hệ Thống"** - Khởi tạo lại toàn bộ
- **"Reset Hệ Thống"** - Reset về mặc định
- **"Hiển Thị Thông Tin"** - Xem trạng thái hệ thống
- **"Test Mua Vật Phẩm"** - Test mua wood
- **"Test Bán Vật Phẩm"** - Test bán wood

#### LeaCurrencyManager
- **"Thêm 1000 Lea"** - Thêm tiền test
- **"Trừ 500 Lea"** - Trừ tiền test
- **"Reset Tiền"** - Reset về 100 Lea
- **"Hiển Thị Thông Tin"** - Xem thông tin tiền

#### PlayerInventory
- **"Thêm Vật Phẩm Test"** - Thêm vật phẩm test
- **"Xóa Vật Phẩm Test"** - Xóa vật phẩm test
- **"Hiển Thị Thông Tin Kho"** - Xem danh sách kho

#### UI Components
- **LeaCurrencyUI:** Test animations
- **InventoryUI:** Mở/đóng kho, cập nhật UI

### Kiểm Tra Hoạt Động

1. **Test Currency System:**
   - Right-click LeaCurrencyManager → "Thêm 1000 Lea"
   - Kiểm tra UI có animation không
   - Kiểm tra màu sắc thay đổi

2. **Test Inventory System:**
   - Right-click PlayerInventory → "Thêm Vật Phẩm Test"
   - Nhấn Tab để mở kho
   - Kiểm tra tìm kiếm và sắp xếp

3. **Test Integration:**
   - Right-click EconomySystemManager → "Test Mua Vật Phẩm"
   - Kiểm tra tiền giảm và vật phẩm tăng
   - Test bán vật phẩm

---

## ⚠️ Lưu Ý Quan Trọng

### Cài Đặt Inspector Fields
- **Tất cả fields đều có thể điều chỉnh trong Edit mode**
- **Không cần Play mode để cấu hình**
- **Sử dụng Context Menu để test nhanh**

### Performance
- **Auto-save:** Mặc định 30-60 giây
- **UI Updates:** Chỉ khi có thay đổi
- **Memory:** Tối ưu với object pooling

### Tương Thích
- **Unity 2021.3+**
- **TextMeshPro required**
- **Input System (optional)**

### Troubleshooting
- **UI không hiển thị:** Kiểm tra Canvas và references
- **Không lưu dữ liệu:** Kiểm tra PlayerPrefs permissions
- **Animation không hoạt động:** Kiểm tra Coroutine settings

---

## 📞 Hỗ Trợ

Nếu gặp vấn đề, kiểm tra:
1. **Console logs** - Bật "Hiển Thị Log" trong các Manager
2. **Component references** - Đảm bảo đã gán đầy đủ
3. **Context Menu** - Sử dụng để debug nhanh

**Chúc bạn thành công với hệ thống Economy Lea! 🎮💰**
