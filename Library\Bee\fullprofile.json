{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 28080, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 28080, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 28080, "tid": 282098, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 28080, "tid": 282098, "ts": 1749799226455243, "dur": 788, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226459849, "dur": 1067, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 28080, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 28080, "tid": 1, "ts": 1749799225473092, "dur": 4993, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 28080, "tid": 1, "ts": 1749799225478093, "dur": 38913, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 28080, "tid": 1, "ts": 1749799225517020, "dur": 32253, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226460922, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 28080, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225471281, "dur": 8721, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225480007, "dur": 965235, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225481821, "dur": 2962, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225484789, "dur": 1334, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486125, "dur": 187, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486315, "dur": 10, "ph": "X", "name": "ProcessMessages 20541", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486326, "dur": 29, "ph": "X", "name": "ReadAsync 20541", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486357, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486380, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486403, "dur": 17, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486422, "dur": 17, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486441, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486461, "dur": 141, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486606, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486633, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486653, "dur": 19, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486674, "dur": 18, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486694, "dur": 19, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486715, "dur": 17, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486734, "dur": 23, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486761, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486763, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486818, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486821, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486848, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225486850, "dur": 450, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487307, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487310, "dur": 153, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487465, "dur": 8, "ph": "X", "name": "ProcessMessages 7862", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487474, "dur": 52, "ph": "X", "name": "ReadAsync 7862", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487528, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487531, "dur": 70, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487604, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487607, "dur": 46, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487655, "dur": 1, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487658, "dur": 46, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487705, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487707, "dur": 33, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487743, "dur": 70, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487819, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487859, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487861, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487887, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487908, "dur": 19, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487929, "dur": 18, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487949, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487966, "dur": 19, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225487987, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488009, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488030, "dur": 19, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488051, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488071, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488088, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488109, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488130, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488151, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488171, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488193, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488213, "dur": 18, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488233, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488254, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488275, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488295, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488314, "dur": 11, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488326, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488347, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488371, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488392, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488411, "dur": 17, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488431, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488450, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488469, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488488, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488509, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488528, "dur": 32, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488562, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488581, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488602, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488620, "dur": 19, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488641, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488661, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488680, "dur": 17, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488701, "dur": 18, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488721, "dur": 27, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488749, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488769, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488790, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488812, "dur": 50, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488865, "dur": 26, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488892, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488893, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488914, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488936, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488957, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488977, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225488998, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489017, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489031, "dur": 19, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489052, "dur": 35, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489089, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489124, "dur": 25, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489150, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489152, "dur": 18, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489173, "dur": 21, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489196, "dur": 12, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489210, "dur": 184, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489396, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489431, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489432, "dur": 27, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489461, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489463, "dur": 21, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489485, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489506, "dur": 19, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489528, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489547, "dur": 29, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489580, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489603, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489621, "dur": 17, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489640, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489660, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489681, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489702, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489722, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489741, "dur": 53, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489799, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489801, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489839, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489842, "dur": 42, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489889, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489892, "dur": 43, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489938, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489940, "dur": 36, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489977, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225489979, "dur": 40, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490020, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490023, "dur": 41, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490066, "dur": 4, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490071, "dur": 33, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490106, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490108, "dur": 40, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490150, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490152, "dur": 40, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490194, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490196, "dur": 37, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490235, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490237, "dur": 30, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490270, "dur": 38, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490311, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490313, "dur": 40, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490354, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490356, "dur": 36, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490394, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490396, "dur": 30, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490428, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490429, "dur": 40, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490471, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490473, "dur": 39, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490513, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490515, "dur": 38, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490555, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490557, "dur": 29, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490588, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490590, "dur": 38, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490630, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490632, "dur": 38, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490671, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490673, "dur": 38, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490712, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490714, "dur": 33, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490750, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490781, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490806, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490808, "dur": 27, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490838, "dur": 40, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490879, "dur": 21, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490903, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490924, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490947, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490968, "dur": 20, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225490990, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491009, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491031, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491049, "dur": 19, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491071, "dur": 19, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491091, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491112, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491133, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491157, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491191, "dur": 31, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491224, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491226, "dur": 34, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491261, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491263, "dur": 20, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491285, "dur": 19, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491306, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491328, "dur": 74, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491404, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491433, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491435, "dur": 26, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491465, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491487, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491508, "dur": 18, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491529, "dur": 18, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491549, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491569, "dur": 18, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491589, "dur": 32, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491624, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491648, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491668, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491690, "dur": 20, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491713, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491733, "dur": 18, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491756, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491783, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491786, "dur": 38, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491826, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491829, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491853, "dur": 26, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491881, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491883, "dur": 37, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491926, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225491932, "dur": 65, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492000, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492003, "dur": 45, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492050, "dur": 3, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492054, "dur": 41, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492098, "dur": 2, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492101, "dur": 42, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492145, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492147, "dur": 47, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492197, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492200, "dur": 36, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492238, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492240, "dur": 31, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492274, "dur": 26, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492302, "dur": 28, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492332, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492365, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492394, "dur": 14, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492412, "dur": 29, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492443, "dur": 27, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492472, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492495, "dur": 13, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492510, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492532, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492557, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492579, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492602, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492620, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492643, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492666, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492689, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492715, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492734, "dur": 17, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492752, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492754, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492774, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492797, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492818, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492838, "dur": 14, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492855, "dur": 19, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492877, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492900, "dur": 17, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492921, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492946, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492967, "dur": 18, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225492988, "dur": 20, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493012, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493034, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493058, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493082, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493105, "dur": 28, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493137, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493168, "dur": 19, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493188, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493190, "dur": 39, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493235, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493238, "dur": 36, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493275, "dur": 2, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493278, "dur": 36, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493318, "dur": 2, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493321, "dur": 41, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493364, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493365, "dur": 35, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493404, "dur": 28, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493435, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493436, "dur": 23, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493463, "dur": 19, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493484, "dur": 19, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493517, "dur": 23, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493542, "dur": 156, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493700, "dur": 52, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493755, "dur": 4, "ph": "X", "name": "ProcessMessages 3746", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493759, "dur": 26, "ph": "X", "name": "ReadAsync 3746", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493788, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493790, "dur": 21, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493813, "dur": 21, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493834, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493836, "dur": 21, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493859, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493885, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493904, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493929, "dur": 28, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493960, "dur": 26, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493987, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225493989, "dur": 28, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494019, "dur": 27, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494050, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494088, "dur": 25, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494116, "dur": 26, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494145, "dur": 35, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494183, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494186, "dur": 59, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494247, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494249, "dur": 27, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494277, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494279, "dur": 30, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494312, "dur": 30, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494345, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494347, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494378, "dur": 25, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494406, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494433, "dur": 108, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494543, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494545, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494581, "dur": 1, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494582, "dur": 23, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494607, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494631, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494653, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494655, "dur": 27, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494685, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494688, "dur": 37, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494728, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494747, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494749, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494770, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494772, "dur": 28, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494802, "dur": 95, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494900, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494934, "dur": 3, "ph": "X", "name": "ProcessMessages 2131", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494938, "dur": 30, "ph": "X", "name": "ReadAsync 2131", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494970, "dur": 16, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225494989, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495021, "dur": 27, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495051, "dur": 28, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495081, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495102, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495103, "dur": 29, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495135, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495156, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495161, "dur": 28, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495191, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495193, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495221, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495223, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495251, "dur": 20, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495275, "dur": 27, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495306, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495327, "dur": 20, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495349, "dur": 19, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495372, "dur": 19, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495393, "dur": 16, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495411, "dur": 56, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495469, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495493, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495516, "dur": 19, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495537, "dur": 22, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495563, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495594, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495595, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495633, "dur": 25, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495660, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495662, "dur": 29, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495694, "dur": 25, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495723, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495750, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495782, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495804, "dur": 18, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495825, "dur": 47, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495875, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495897, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495918, "dur": 19, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495939, "dur": 19, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495960, "dur": 35, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225495997, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496022, "dur": 20, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496044, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496066, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496120, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496141, "dur": 51, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496196, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496235, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496237, "dur": 27, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496267, "dur": 67, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496336, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496361, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496381, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496383, "dur": 30, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496416, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496503, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496528, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496549, "dur": 19, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496570, "dur": 27, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496601, "dur": 54, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496658, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496684, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496687, "dur": 17, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496706, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496727, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496782, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496802, "dur": 23, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496827, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496849, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496852, "dur": 56, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496911, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496934, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496962, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225496984, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497038, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497061, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497083, "dur": 18, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497104, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497125, "dur": 61, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497189, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497220, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497246, "dur": 66, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497315, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497349, "dur": 23, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497375, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497395, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497445, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497477, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497479, "dur": 48, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497529, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497531, "dur": 50, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497584, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497612, "dur": 23, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497640, "dur": 19, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497662, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497717, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497746, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497768, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497789, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497848, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497875, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497897, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225497916, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498000, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498031, "dur": 33, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498066, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498068, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498092, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498154, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498182, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498202, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498228, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498255, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498257, "dur": 47, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498307, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498349, "dur": 36, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498386, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498388, "dur": 28, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498421, "dur": 51, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498477, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498504, "dur": 50, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498560, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498565, "dur": 63, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498634, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498669, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498672, "dur": 21, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498696, "dur": 62, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498760, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498787, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498789, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498810, "dur": 18, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498830, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498852, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498873, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225498950, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499004, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499006, "dur": 32, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499040, "dur": 2, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499043, "dur": 24, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499069, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499071, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499099, "dur": 17, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499118, "dur": 19, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499140, "dur": 68, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499214, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499249, "dur": 39, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499290, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499291, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499327, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499371, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499411, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499413, "dur": 26, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499441, "dur": 25, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499468, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499471, "dur": 21, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499495, "dur": 20, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499519, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499542, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499566, "dur": 18, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499586, "dur": 30, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499618, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499639, "dur": 15, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499657, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499788, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499834, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499836, "dur": 18, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225499857, "dur": 236, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500098, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500196, "dur": 2, "ph": "X", "name": "ProcessMessages 2993", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500199, "dur": 30, "ph": "X", "name": "ReadAsync 2993", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500231, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500233, "dur": 25, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500259, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500261, "dur": 71, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500333, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500355, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500357, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500380, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500404, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500475, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500495, "dur": 18, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500515, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500539, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500562, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500585, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500608, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500631, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500652, "dur": 19, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500674, "dur": 18, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500695, "dur": 56, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500753, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500776, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500799, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500822, "dur": 19, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500843, "dur": 70, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500916, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500942, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500960, "dur": 18, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225500980, "dur": 58, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501042, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501065, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501088, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501111, "dur": 79, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501196, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501200, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501277, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501280, "dur": 35, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501320, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501347, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501372, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501398, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501420, "dur": 21, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501447, "dur": 65, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501516, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501541, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501563, "dur": 19, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501583, "dur": 62, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501647, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501670, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501692, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501715, "dur": 59, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501775, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501807, "dur": 20, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501831, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501851, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501912, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501942, "dur": 31, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501974, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225501976, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502002, "dur": 40, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502048, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502091, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502095, "dur": 23, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502119, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502121, "dur": 70, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502194, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502226, "dur": 19, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502247, "dur": 80, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502334, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502387, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502389, "dur": 43, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502437, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502441, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502470, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502507, "dur": 24, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502532, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502534, "dur": 18, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502555, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502556, "dur": 50, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502609, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502645, "dur": 24, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502670, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502672, "dur": 51, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502726, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502757, "dur": 86, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502846, "dur": 27, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502875, "dur": 23, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502899, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502924, "dur": 25, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502952, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225502973, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503035, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503057, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503080, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503102, "dur": 66, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503170, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503196, "dur": 39, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503237, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503239, "dur": 65, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503310, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503313, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503363, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503365, "dur": 21, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503389, "dur": 61, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503453, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503475, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503498, "dur": 25, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503527, "dur": 4, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503532, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503559, "dur": 37, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503599, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503626, "dur": 33, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503661, "dur": 25, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503689, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503690, "dur": 48, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503740, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503771, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503799, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503827, "dur": 97, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503928, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503965, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503967, "dur": 29, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225503999, "dur": 46, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504050, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504088, "dur": 25, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504117, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504119, "dur": 59, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504180, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504208, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504231, "dur": 19, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504253, "dur": 66, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504321, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504344, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504373, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504397, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504399, "dur": 49, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504451, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504474, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504497, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504518, "dur": 18, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504538, "dur": 27, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504567, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504596, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504619, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504643, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504714, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504716, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504753, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504755, "dur": 21, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504779, "dur": 22, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504802, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504803, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504826, "dur": 20, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504848, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504870, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504888, "dur": 14, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504905, "dur": 19, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225504925, "dur": 83, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505013, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505065, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505067, "dur": 40, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505109, "dur": 2, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505113, "dur": 32, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505146, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505147, "dur": 19, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505168, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505190, "dur": 23, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505216, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505265, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505294, "dur": 164, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505466, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225505535, "dur": 607, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506148, "dur": 93, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506245, "dur": 14, "ph": "X", "name": "ProcessMessages 2208", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506260, "dur": 28, "ph": "X", "name": "ReadAsync 2208", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506291, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506294, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506330, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506332, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506365, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506368, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506395, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506398, "dur": 44, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506445, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506447, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506485, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506487, "dur": 26, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506515, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506519, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506547, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506550, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506576, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506580, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506610, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506613, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506639, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506642, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506672, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506675, "dur": 23, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506700, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506702, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506737, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506740, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506778, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506782, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506816, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506819, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506852, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506855, "dur": 39, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506897, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506899, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506932, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506935, "dur": 32, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506970, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225506972, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507004, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507007, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507038, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507041, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507076, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507078, "dur": 35, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507117, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507120, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507158, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507161, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507197, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507200, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507240, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507243, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507281, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507284, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507316, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507318, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507364, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507366, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507397, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507400, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507439, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507440, "dur": 130, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507573, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507576, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507611, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507614, "dur": 32, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507649, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507652, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507683, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507687, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507719, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507723, "dur": 23, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507750, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225507775, "dur": 1887, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225509669, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225509673, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225509727, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225509730, "dur": 1592, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511325, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511327, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511360, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511362, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511393, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511676, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225511705, "dur": 1961, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513673, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513707, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513739, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513741, "dur": 127, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513872, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225513897, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514177, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514212, "dur": 239, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514457, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514492, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514494, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514536, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514538, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514569, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514571, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514638, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514663, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514758, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514781, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225514977, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515006, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515009, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515033, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515035, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515059, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515082, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515126, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515165, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515167, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515198, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515200, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515230, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515279, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515311, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515312, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515344, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515381, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515383, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515424, "dur": 336, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515765, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515806, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515842, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515870, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515873, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515909, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515936, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225515980, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516003, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516027, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516049, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516078, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516080, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516106, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516130, "dur": 603, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516737, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516769, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516771, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516825, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516828, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516869, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516872, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516902, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516905, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516940, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225516965, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517018, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517040, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517042, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517126, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517153, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517186, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517233, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517262, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517285, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517415, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517436, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517462, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517491, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517516, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517536, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517560, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517593, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517619, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517652, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517680, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517703, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517762, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517797, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517821, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517868, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517892, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225517911, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518038, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518076, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518077, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518112, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518153, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518181, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518221, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518265, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518267, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518341, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518370, "dur": 434, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518806, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518837, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518839, "dur": 44, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518887, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518925, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225518955, "dur": 442, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519401, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519438, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519440, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519509, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519544, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519578, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519650, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519683, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519728, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519756, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519778, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519780, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225519982, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520002, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520051, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520086, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520113, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520116, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520148, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520181, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520453, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520481, "dur": 218, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520702, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520728, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520768, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520770, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520790, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520823, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520849, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520871, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520895, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225520917, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521262, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521291, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521293, "dur": 203, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521500, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521535, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521537, "dur": 319, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521860, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521892, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521894, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521968, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225521992, "dur": 484, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522479, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522517, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522624, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522656, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522683, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522728, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522752, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522855, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225522873, "dur": 510, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225523387, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225523404, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225523407, "dur": 84929, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225608353, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225608360, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225608397, "dur": 1942, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225610347, "dur": 2757, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613112, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613116, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613150, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613152, "dur": 124, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613282, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613310, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613344, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613392, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613422, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225613424, "dur": 1572, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615001, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615038, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615040, "dur": 447, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615494, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615538, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615540, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615662, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615696, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615726, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615750, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615821, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615852, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615853, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225615999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225616029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225616030, "dur": 1410, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617446, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617489, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617516, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617551, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617583, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617584, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617696, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617732, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225617734, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618044, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618048, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618074, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618076, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618192, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618221, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618344, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618362, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618383, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618542, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618567, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618627, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618649, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618907, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225618938, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619062, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619099, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619134, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619166, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619824, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225619861, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620074, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620115, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620117, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620148, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620150, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620420, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620426, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620454, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620565, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620591, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620701, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225620725, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621004, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621027, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621029, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621125, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621156, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621189, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621191, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621216, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621218, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621248, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621251, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621279, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621281, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621316, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621318, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621358, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621361, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621392, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621394, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621424, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621426, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621461, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621463, "dur": 32, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621499, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621501, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621536, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621540, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621584, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621587, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621622, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621624, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621663, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621665, "dur": 29, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621697, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621700, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621726, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621728, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621759, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621761, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621793, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621794, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621822, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621824, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621857, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621860, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621891, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621918, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621966, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225621996, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622106, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622132, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622151, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622187, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622281, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622301, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622359, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622392, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622394, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622419, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622516, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622543, "dur": 130, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622676, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622704, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622727, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225622757, "dur": 118718, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225741484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225741488, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225741512, "dur": 20, "ph": "X", "name": "ProcessMessages 3847", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225741533, "dur": 26699, "ph": "X", "name": "ReadAsync 3847", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225768241, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225768246, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225768303, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225768306, "dur": 166992, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935308, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935313, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935371, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935377, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935418, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935467, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799225935472, "dur": 98201, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226033684, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226033688, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226033715, "dur": 23, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226033739, "dur": 5740, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226039487, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226039490, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226039532, "dur": 44362, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226083904, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226083910, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226083978, "dur": 35, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226084015, "dur": 6670, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226090691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226090694, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226090742, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226090745, "dur": 1500, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226092248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226092250, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226092279, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226092297, "dur": 175848, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268154, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268158, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268204, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268208, "dur": 696, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268908, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268946, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226268976, "dur": 166797, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226435785, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226435791, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226435850, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226435852, "dur": 879, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226436739, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226436742, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226436812, "dur": 392, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 28080, "tid": 12884901888, "ts": 1749799226437207, "dur": 7288, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226460942, "dur": 1585, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 28080, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 28080, "tid": 8589934592, "ts": 1749799225468834, "dur": 80527, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 28080, "tid": 8589934592, "ts": 1749799225549365, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 28080, "tid": 8589934592, "ts": 1749799225549372, "dur": 1529, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226462530, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 28080, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 28080, "tid": 4294967296, "ts": 1749799225450380, "dur": 995714, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 28080, "tid": 4294967296, "ts": 1749799225454172, "dur": 8505, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 28080, "tid": 4294967296, "ts": 1749799226446264, "dur": 5555, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 28080, "tid": 4294967296, "ts": 1749799226449394, "dur": 344, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 28080, "tid": 4294967296, "ts": 1749799226451927, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226462537, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749799225476930, "dur": 1740, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799225478678, "dur": 915, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799225479726, "dur": 67, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749799225479794, "dur": 536, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799225481749, "dur": 2492, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F75569858C7FEC6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749799225485300, "dur": 1216, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749799225487529, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749799225480350, "dur": 25125, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799225505489, "dur": 930508, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799226435999, "dur": 519, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799226436811, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749799226436920, "dur": 1220, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749799225480675, "dur": 24847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225505526, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749799225505921, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749799225506599, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225506735, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749799225506808, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225506938, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749799225507360, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225507468, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749799225507613, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225507900, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225508138, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225508356, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225508591, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225508785, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225508972, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225509156, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225509755, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225509937, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225510117, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225510350, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225510536, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225510911, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225511451, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225511644, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225511879, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225512115, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225512345, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225512702, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225513024, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225513231, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225513448, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225513922, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225514449, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749799225514652, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225515325, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225515905, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225516034, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225516632, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225517039, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225517428, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225517713, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225518280, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749799225518423, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225518760, "dur": 3960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225522721, "dur": 88541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225611263, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225613282, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225615252, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225615701, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225618585, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225618744, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225620992, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225621207, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749799225621567, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225621843, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225622053, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749799225622585, "dur": 813556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225480612, "dur": 24889, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225505521, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225505700, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_100C028F850EE8CE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749799225505911, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225506380, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749799225506583, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225506698, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225507013, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749799225507493, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749799225507639, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225507891, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225508131, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225508346, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225508748, "dur": 1469, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Utilities\\pCache\\BakeTool\\PointCacheBakeTool.Texture.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749799225508569, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225510240, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225510430, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225510635, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225510818, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225511007, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225511197, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225511383, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225511591, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225511826, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225512123, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225512329, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225512499, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225512677, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225512886, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225513077, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225513442, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225513909, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225514426, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749799225514635, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225514699, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225515229, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749799225515408, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225515549, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225516592, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225516642, "dur": 421, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225517084, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225517705, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225518290, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749799225518608, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225519116, "dur": 3639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225522755, "dur": 88526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225611285, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225613230, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749799225613323, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225615187, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225618250, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225620333, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749799225622894, "dur": 813181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225480665, "dur": 24849, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225505527, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225505916, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225506355, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749799225506593, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225506710, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749799225506763, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749799225506938, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749799225507364, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225507432, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749799225507637, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225507905, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225508111, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225508366, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225508587, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225508779, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225508976, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225509163, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225509715, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225510145, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225510460, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225510757, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225510953, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225511148, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225511357, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225511565, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225511795, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225512095, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225512410, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225512613, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225512794, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225513006, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225513331, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225513401, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225513920, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225514457, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749799225514658, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225515350, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749799225515545, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225515864, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749799225516014, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225516615, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225517039, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749799225517233, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225517801, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225518280, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749799225518432, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225518764, "dur": 3974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225522738, "dur": 90535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225613280, "dur": 5940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225619262, "dur": 2059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749799225621511, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749799225621640, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749799225621785, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225621862, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225621923, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225622026, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225622158, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749799225622948, "dur": 813115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225480732, "dur": 24800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225505536, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749799225505820, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749799225505904, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225506467, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225506578, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225506685, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225506967, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225507068, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749799225507125, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749799225507357, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225507621, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225507785, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749799225507907, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225508258, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225508493, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225508689, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225508882, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225509100, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225509633, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225509824, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510018, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510193, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510375, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510555, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510729, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225510938, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225511132, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225511317, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225511505, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225511698, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225511889, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225512168, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225512365, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225512559, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225512914, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225512966, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225513029, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225513396, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225513895, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225514444, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749799225514680, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225515396, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225515902, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225515954, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225516362, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225516632, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749799225517053, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749799225517363, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225517842, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225518449, "dur": 4268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225522717, "dur": 28389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225552429, "dur": 133, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1749799225552562, "dur": 968, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 4, "ts": 1749799225553530, "dur": 53, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 4, "ts": 1749799225551108, "dur": 2484, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225553593, "dur": 57680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225611278, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225613229, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225613511, "dur": 3986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225617498, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225617891, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749799225620551, "dur": 763, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225621325, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225621595, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225622090, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749799225622899, "dur": 813205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225480775, "dur": 24773, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225505559, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225505909, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225506583, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225506934, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1749799225507016, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749799225507429, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749799225507633, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225507919, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225508181, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225508438, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225508825, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225509079, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225509259, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225509877, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225510115, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225510299, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225510476, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225510685, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225510879, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225511102, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225511356, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225511574, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225511854, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225512129, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225512319, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225512513, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225512692, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225512891, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225513119, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225513524, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225513896, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225514422, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225514684, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225515268, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225515330, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225515560, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225516229, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225517055, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749799225517128, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225517696, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1749799225518127, "dur": 79, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225518550, "dur": 89944, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1749799225611259, "dur": 1970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225613265, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225613606, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225616032, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225618180, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749799225618243, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225620610, "dur": 1925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749799225622581, "dur": 813425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225480812, "dur": 24753, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225505571, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3F8B9D82C8E678D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225505762, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225505924, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225506381, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225506533, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225506610, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225506750, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225507265, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225507388, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225507498, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225507564, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749799225507633, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225507882, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225508077, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225508265, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225508466, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225508663, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225508886, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225509075, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225509263, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225509743, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225509920, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225510111, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225510351, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225510530, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225510706, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225510897, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225511106, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225511723, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225511897, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225512108, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225512290, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225512494, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225512694, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225512882, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225513477, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225513922, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225514446, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225514644, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225514960, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225516268, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225516357, "dur": 576, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225516935, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225517091, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225517638, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225517738, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225518290, "dur": 4421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225522721, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749799225522865, "dur": 88426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225611295, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225613273, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225613601, "dur": 3296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225616899, "dur": 842, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225617752, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225619869, "dur": 1023, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749799225620902, "dur": 1768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749799225622728, "dur": 813418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225480850, "dur": 24725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225505763, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225505908, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225506392, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749799225506527, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225506612, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225506768, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225506849, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225507434, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749799225507541, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749799225507611, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225507769, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749799225507898, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225508082, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225508274, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225508576, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225508778, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225508957, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225509196, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225509696, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225509898, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225510106, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225510289, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225510474, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225510664, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225510876, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225511081, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225511267, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225511465, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225511650, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225511896, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225512364, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225512544, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225512722, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225512902, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225513200, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225513482, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225513902, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225514424, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225514713, "dur": 1704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225516444, "dur": 517, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225516996, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225517217, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225517316, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225518276, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225518413, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225518896, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225519077, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225519727, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225519845, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225520991, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225521099, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225522061, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225522166, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225522713, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749799225522821, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225523059, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225523903, "dur": 217799, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225743298, "dur": 24916, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749799225742989, "dur": 25331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225768323, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225768395, "dur": 164378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749799225768391, "dur": 165704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749799225935440, "dur": 165, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749799225935989, "dur": 97958, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749799226039534, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749799226039526, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749799226039649, "dur": 396366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225480882, "dur": 24708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225505874, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9112B3CA45DA26CE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749799225505930, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749799225506302, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225506467, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225506605, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225506767, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225506882, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749799225507427, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749799225507617, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225507884, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225508110, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225508385, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225508592, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225508808, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225509029, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225509226, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225509806, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225510142, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225510744, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225510961, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225511150, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225511350, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225511559, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225511791, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225512018, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225512225, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225512438, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225512701, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225512980, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225513164, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225513478, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225513927, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225514760, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749799225514962, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225515377, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225516035, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225516517, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225517075, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225517134, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225517743, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225518294, "dur": 4421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225522717, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749799225522931, "dur": 90613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225613547, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225615839, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225615917, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225618163, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225618390, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225620749, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749799225621230, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749799225622952, "dur": 813095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225480916, "dur": 24689, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225505689, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_5D5EF2E9E0015A01.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225505748, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225505915, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225506454, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749799225506558, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225506663, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225506772, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225506860, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749799225507138, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225507519, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749799225507620, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225507911, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225508111, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225508336, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225508542, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225508736, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225508933, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225509138, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225509625, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225509814, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225510054, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225510384, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225510720, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225510973, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225511165, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225511916, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225512201, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225512395, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225512574, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225512759, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225512968, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225513509, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225513908, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225514449, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225514651, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225514848, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225515077, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225515389, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225515926, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225516268, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225516420, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225516502, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225516980, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225517647, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225517699, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225518287, "dur": 1907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225520195, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225520374, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225520918, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225521029, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225521359, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749799225521457, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225521700, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225522725, "dur": 88532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225611262, "dur": 1971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225613290, "dur": 1856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225615148, "dur": 865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225616024, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225618540, "dur": 2947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749799225621527, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225621767, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749799225621834, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225622043, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799225622361, "dur": 468360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749799226090740, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749799226090723, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749799226090867, "dur": 1573, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1749799226092443, "dur": 343557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225480971, "dur": 24792, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225505907, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225506394, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225506712, "dur": 4760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225511551, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225511872, "dur": 1945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225513928, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225514067, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225514422, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225514646, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225515205, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225515341, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225516155, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225516341, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225517005, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225517465, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225518011, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225518283, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225518900, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225519023, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225519500, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225519604, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749799225519746, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225520262, "dur": 2457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225522719, "dur": 30877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225553597, "dur": 57666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225611269, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225613263, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225615196, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225618338, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225618832, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749799225620946, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225621355, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225621829, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225622058, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749799225622616, "dur": 813505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225481036, "dur": 24614, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225505660, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7B06E7D06A357F34.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225505761, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A0D61AB0BD46DF64.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225505903, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225506463, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225506524, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749799225506587, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225507046, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749799225507613, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225507880, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225508065, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225508317, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225508529, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225508724, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225508941, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225509136, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225509628, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225509819, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225510050, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225510293, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225510484, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225510699, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225510891, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225511094, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225511290, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225511483, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225511663, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225511869, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225512105, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225512292, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225512467, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225512667, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225512853, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225513058, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225513326, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225513401, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225513907, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225514443, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225514665, "dur": 872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225515544, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225516037, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225516202, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225516295, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225516738, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225517119, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225517352, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225517443, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225517879, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225518285, "dur": 1447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225519733, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749799225519917, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225520317, "dur": 2409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225522726, "dur": 90734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225613465, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225615813, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225615947, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225617963, "dur": 1135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225619110, "dur": 2052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225621208, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749799225621482, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225621678, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225622047, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749799225622500, "dur": 813656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225481094, "dur": 24571, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225505763, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_69A7891FFD914456.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225505889, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_F7BBE303F716E2E7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225506274, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225506359, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749799225506531, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749799225506591, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225506786, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225506941, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749799225507214, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749799225507363, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225507494, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749799225507609, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225507814, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749799225507896, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225508195, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225508652, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225508857, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225509061, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225509257, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225510027, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225510213, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225510395, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225510575, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225510762, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225511010, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225511214, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225511415, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225511610, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225511806, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225512039, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225512708, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225512875, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225513064, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225513396, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225513893, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225514442, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225514706, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225515107, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225515187, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225515339, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225515570, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225515971, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225516629, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225517014, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225517217, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225517710, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225518094, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225518282, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225518853, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749799225518971, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225519958, "dur": 2769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225522727, "dur": 90746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225613479, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225615854, "dur": 3378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225619233, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225619339, "dur": 2053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225621393, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225621619, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749799225621858, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225622039, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225622276, "dur": 120722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225743030, "dur": 189778, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749799225743001, "dur": 191088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749799225935170, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749799225935861, "dur": 148192, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749799226090722, "dur": 177540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749799226090715, "dur": 177550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749799226268295, "dur": 792, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749799226269091, "dur": 167043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225481156, "dur": 24522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225505927, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225506404, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225506573, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225509920, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225510153, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225510679, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225510861, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225511052, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225511236, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225511429, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225511608, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225511925, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225512182, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225512400, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225512612, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double4.gen.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749799225512589, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225513450, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225513938, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225514433, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225514761, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225515151, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225515574, "dur": 404, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_407F07063643C512.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225515981, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225516224, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225516731, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225517097, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225517797, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749799225517963, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225518350, "dur": 4380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225522731, "dur": 90573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225613309, "dur": 4285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225617632, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225619776, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749799225620014, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749799225622483, "dur": 813513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225481191, "dur": 24509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225505708, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225505762, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225505916, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225506213, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225506482, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225506569, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225506672, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749799225507005, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749799225507346, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749799225507637, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225507788, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749799225507889, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225508089, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225508316, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225508542, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225508760, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225508955, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225509143, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225509680, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225509879, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225510656, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225510856, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225511079, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225511265, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225511482, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225511785, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225512050, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225512257, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225512470, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225512669, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225512847, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225513237, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225513436, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225513932, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225514704, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225514848, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225515478, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225515982, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225516063, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225516214, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225516270, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225516433, "dur": 509, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225516943, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225517480, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225517854, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225518288, "dur": 2635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225520924, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749799225521050, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225521335, "dur": 1410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225522745, "dur": 90545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225613297, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225615138, "dur": 774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225615920, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225617857, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225618536, "dur": 2179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749799225620716, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225621404, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749799225621550, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749799225621666, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225621736, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749799225621930, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225622068, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749799225622735, "dur": 813359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225481235, "dur": 24483, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225505722, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_46081861FEA49A1E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225505915, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225506559, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225506783, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225506871, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225507357, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225507517, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749799225507613, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225507772, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749799225507887, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225508192, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225508493, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225508694, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225508920, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225509100, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225509608, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225509808, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510007, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510190, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510411, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510587, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510764, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225510964, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225511164, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225511352, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225511561, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225511803, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225512199, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225512494, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225512695, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225512889, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225513194, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225513459, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225513934, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225514437, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225514691, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225515127, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225515225, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225515460, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225515536, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225516043, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225516501, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225516999, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225517191, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225517353, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225518762, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225518849, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225519014, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225520190, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749799225520313, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225520650, "dur": 2092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225522743, "dur": 90755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225613503, "dur": 2593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225616097, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225616203, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225618279, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225618578, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225620700, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749799225620761, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749799225622605, "dur": 813546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225481286, "dur": 24439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225505933, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749799225506371, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749799225506585, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225506840, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749799225507082, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225507351, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225507634, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225507851, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749799225507949, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225508216, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225508444, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225508668, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225508867, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225509085, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225509263, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225509871, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225510178, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225510472, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225510785, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225510990, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225511172, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225511367, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225511551, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225511760, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225512108, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225512296, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225512471, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225512704, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225512902, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225513142, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225513509, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225513899, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225514430, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749799225514687, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225515194, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225516052, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225516520, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225516707, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225517089, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225517710, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225518278, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749799225518434, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225518713, "dur": 4009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225522724, "dur": 88536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225611261, "dur": 1971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225613234, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799225613340, "dur": 1816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225615191, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225617706, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225620266, "dur": 2026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749799225622351, "dur": 417182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749799226039568, "dur": 396302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749799226039535, "dur": 396338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749799226443024, "dur": 1258, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 28080, "tid": 282098, "ts": 1749799226463070, "dur": 3569, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 28080, "tid": 282098, "ts": 1749799226466693, "dur": 3336, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 28080, "tid": 282098, "ts": 1749799226458323, "dur": 12813, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}