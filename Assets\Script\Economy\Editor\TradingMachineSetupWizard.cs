using UnityEngine;
using UnityEditor;
using TMPro;
using UnityEngine.UI;

namespace EconomySystem.Editor
{
    /// <summary>
    /// Wizard để setup nhanh Trading Machine
    /// </summary>
    public class TradingMachineSetupWizard : EditorWindow
    {
        #region Private Fields
        private Vector2 m_ScrollPosition;
        private string m_TenMay = "Máy Giao Dịch";
        private string m_ItemId = "wood";
        private TradingMachine.TradingMode m_LoaiMay = TradingMachine.TradingMode.Both;
        private int m_GiaMua = 10;
        private int m_GiaBan = 15;
        private bool m_TaoModel = true;
        private bool m_TaoUI = true;
        private bool m_TaoEffects = true;
        private Vector3 m_ViTri = Vector3.zero;
        #endregion

        #region Menu Item
        [MenuItem("Economy System/Trading Machine Setup Wizard", priority = 2)]
        public static void ShowWindow()
        {
            TradingMachineSetupWizard window = GetWindow<TradingMachineSetupWizard>("Trading Machine Setup");
            window.minSize = new Vector2(450, 700);
            window.Show();
        }
        #endregion

        #region GUI
        private void OnGUI()
        {
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
            
            DrawHeader();
            DrawMachineSettings();
            DrawSetupOptions();
            DrawButtons();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.Space(10);
            
            GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 18;
            titleStyle.alignment = TextAnchor.MiddleCenter;
            
            EditorGUILayout.LabelField("🏪 Trading Machine Setup Wizard", titleStyle);
            EditorGUILayout.LabelField("Thiết lập nhanh máy mua/bán với trigger zones", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        }

        private void DrawMachineSettings()
        {
            EditorGUILayout.LabelField("⚙️ Cài Đặt Máy", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TenMay = EditorGUILayout.TextField("Tên Máy", m_TenMay);
            m_ItemId = EditorGUILayout.TextField("Item ID", m_ItemId);
            m_LoaiMay = (TradingMachine.TradingMode)EditorGUILayout.EnumPopup("Loại Máy", m_LoaiMay);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("💰 Cài Đặt Giá", EditorStyles.boldLabel);
            m_GiaMua = EditorGUILayout.IntField("Giá Mua (Máy mua từ player)", m_GiaMua);
            m_GiaBan = EditorGUILayout.IntField("Giá Bán (Máy bán cho player)", m_GiaBan);
            
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("📍 Vị Trí", EditorStyles.boldLabel);
            m_ViTri = EditorGUILayout.Vector3Field("Vị Trí Đặt Máy", m_ViTri);
            
            EditorGUILayout.Space(10);
        }

        private void DrawSetupOptions()
        {
            EditorGUILayout.LabelField("🔧 Tùy Chọn Thiết Lập", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);
            
            m_TaoModel = EditorGUILayout.Toggle("Tạo Model 3D", m_TaoModel);
            m_TaoUI = EditorGUILayout.Toggle("Tạo UI System", m_TaoUI);
            m_TaoEffects = EditorGUILayout.Toggle("Tạo Effects & Audio", m_TaoEffects);
            
            EditorGUILayout.Space(10);
        }

        private void DrawButtons()
        {
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
            EditorGUILayout.Space(10);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🏪 Tạo Trading Machine", GUILayout.Height(40)))
            {
                TaoTradingMachine();
            }
            
            if (GUILayout.Button("📖 Mở Hướng Dẫn", GUILayout.Height(40)))
            {
                MoHuongDan();
            }
            
            GUILayout.EndHorizontal();
            
            EditorGUILayout.Space(10);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔍 Kiểm Tra Economy System", GUILayout.Height(30)))
            {
                KiemTraEconomySystem();
            }
            
            if (GUILayout.Button("🎯 Tìm Trading Machines", GUILayout.Height(30)))
            {
                TimTradingMachines();
            }
            
            GUILayout.EndHorizontal();
        }
        #endregion

        #region Setup Methods
        private void TaoTradingMachine()
        {
            try
            {
                EditorUtility.DisplayProgressBar("Trading Machine Setup", "Đang tạo Trading Machine...", 0f);
                
                // Tạo GameObject chính
                GameObject tradingMachine = new GameObject($"TradingMachine_{m_ItemId}");
                tradingMachine.transform.position = m_ViTri;
                
                // Thêm components chính
                EditorUtility.DisplayProgressBar("Trading Machine Setup", "Thêm components...", 0.2f);
                TradingMachine machine = tradingMachine.AddComponent<TradingMachine>();
                TradingZoneDetector detector = tradingMachine.AddComponent<TradingZoneDetector>();
                TradingMachineUI machineUI = tradingMachine.AddComponent<TradingMachineUI>();
                
                // Cấu hình Trading Machine
                EditorUtility.DisplayProgressBar("Trading Machine Setup", "Cấu hình máy...", 0.4f);
                CauHinhTradingMachine(machine);
                
                // Tạo Model 3D
                if (m_TaoModel)
                {
                    EditorUtility.DisplayProgressBar("Trading Machine Setup", "Tạo model 3D...", 0.6f);
                    TaoModel3D(tradingMachine);
                }
                
                // Tạo UI System
                if (m_TaoUI)
                {
                    EditorUtility.DisplayProgressBar("Trading Machine Setup", "Tạo UI system...", 0.8f);
                    TaoUISystem(tradingMachine, machine, machineUI);
                }
                
                // Tạo Effects
                if (m_TaoEffects)
                {
                    EditorUtility.DisplayProgressBar("Trading Machine Setup", "Tạo effects...", 0.9f);
                    TaoEffects(tradingMachine, machine);
                }
                
                // Thiết lập Trigger Zone
                ThietLapTriggerZone(tradingMachine, detector);
                
                EditorUtility.DisplayProgressBar("Trading Machine Setup", "Hoàn thành!", 1f);
                
                // Select object đã tạo
                Selection.activeGameObject = tradingMachine;
                
                EditorUtility.DisplayDialog("Thành Công!", 
                    $"Đã tạo Trading Machine: {m_TenMay}\n\n" +
                    $"Item: {m_ItemId}\n" +
                    $"Loại: {m_LoaiMay}\n" +
                    $"Giá mua: {m_GiaMua} Lea\n" +
                    $"Giá bán: {m_GiaBan} Lea\n\n" +
                    "Hãy test bằng cách:\n" +
                    "1. Play game\n" +
                    "2. Di chuyển Player vào trigger zone\n" +
                    "3. Nhấn F để mở UI", 
                    "OK");
            }
            catch (System.Exception e)
            {
                EditorUtility.DisplayDialog("Lỗi!", $"Có lỗi xảy ra: {e.Message}", "OK");
                Debug.LogError($"[TradingMachineSetupWizard] Lỗi: {e.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void CauHinhTradingMachine(TradingMachine machine)
        {
            SerializedObject so = new SerializedObject(machine);
            
            so.FindProperty("m_LoaiMay").enumValueIndex = (int)m_LoaiMay;
            so.FindProperty("m_TenMay").stringValue = m_TenMay;
            so.FindProperty("m_ItemId").stringValue = m_ItemId;
            so.FindProperty("m_GiaMua").intValue = m_GiaMua;
            so.FindProperty("m_GiaBan").intValue = m_GiaBan;
            so.FindProperty("m_HienThiLog").boolValue = true;
            so.FindProperty("m_ChoPhepChinhGia").boolValue = true;
            
            so.ApplyModifiedProperties();
        }

        private void TaoModel3D(GameObject parent)
        {
            // Tạo container cho model
            GameObject modelContainer = new GameObject("Model");
            modelContainer.transform.SetParent(parent.transform, false);
            
            // Tạo base (thân máy)
            GameObject baseObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            baseObj.name = "Base";
            baseObj.transform.SetParent(modelContainer.transform, false);
            baseObj.transform.localScale = new Vector3(2f, 3f, 1f);
            baseObj.transform.localPosition = new Vector3(0, 1.5f, 0);
            
            // Tạo screen
            GameObject screenObj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            screenObj.name = "Screen";
            screenObj.transform.SetParent(modelContainer.transform, false);
            screenObj.transform.localPosition = new Vector3(0, 2f, 0.51f);
            screenObj.transform.localScale = new Vector3(1.5f, 1f, 1f);
            
            // Tạo indicators
            TaoIndicators(parent);
            
            Debug.Log("[TradingMachineSetupWizard] Đã tạo model 3D");
        }

        private void TaoIndicators(GameObject parent)
        {
            GameObject indicatorContainer = new GameObject("Indicators");
            indicatorContainer.transform.SetParent(parent.transform, false);
            
            // Buy indicator (xanh lá)
            if (m_LoaiMay == TradingMachine.TradingMode.Buy || m_LoaiMay == TradingMachine.TradingMode.Both)
            {
                GameObject buyIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                buyIndicator.name = "BuyIndicator";
                buyIndicator.transform.SetParent(indicatorContainer.transform, false);
                buyIndicator.transform.localPosition = new Vector3(-0.7f, 3.5f, 0);
                buyIndicator.transform.localScale = Vector3.one * 0.3f;
                
                Renderer buyRenderer = buyIndicator.GetComponent<Renderer>();
                buyRenderer.material.color = Color.green;
            }
            
            // Sell indicator (vàng)
            if (m_LoaiMay == TradingMachine.TradingMode.Sell || m_LoaiMay == TradingMachine.TradingMode.Both)
            {
                GameObject sellIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                sellIndicator.name = "SellIndicator";
                sellIndicator.transform.SetParent(indicatorContainer.transform, false);
                sellIndicator.transform.localPosition = new Vector3(0.7f, 3.5f, 0);
                sellIndicator.transform.localScale = Vector3.one * 0.3f;
                
                Renderer sellRenderer = sellIndicator.GetComponent<Renderer>();
                sellRenderer.material.color = Color.yellow;
            }
            
            // Zone indicator
            GameObject zoneIndicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            zoneIndicator.name = "ZoneIndicator";
            zoneIndicator.transform.SetParent(indicatorContainer.transform, false);
            zoneIndicator.transform.localPosition = new Vector3(0, 0.1f, 0);
            zoneIndicator.transform.localScale = new Vector3(6f, 0.1f, 6f);
            
            Renderer zoneRenderer = zoneIndicator.GetComponent<Renderer>();
            Material zoneMaterial = new Material(Shader.Find("Standard"));
            zoneMaterial.color = new Color(1f, 1f, 0f, 0.3f);
            zoneMaterial.SetFloat("_Mode", 3); // Transparent mode
            zoneMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            zoneMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            zoneMaterial.SetInt("_ZWrite", 0);
            zoneMaterial.DisableKeyword("_ALPHATEST_ON");
            zoneMaterial.EnableKeyword("_ALPHABLEND_ON");
            zoneMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            zoneMaterial.renderQueue = 3000;
            zoneRenderer.material = zoneMaterial;
        }

        private void TaoUISystem(GameObject parent, TradingMachine machine, TradingMachineUI machineUI)
        {
            // Tạo Canvas
            GameObject canvasObj = new GameObject("TradingUI_Canvas");
            canvasObj.transform.SetParent(parent.transform, false);
            canvasObj.transform.localPosition = new Vector3(0, 4f, 0);
            
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.WorldSpace;
            canvas.worldCamera = Camera.main;
            
            CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPhysicalSize;
            
            canvasObj.AddComponent<GraphicRaycaster>();
            
            // Cấu hình Canvas size
            RectTransform canvasRect = canvas.GetComponent<RectTransform>();
            canvasRect.sizeDelta = new Vector2(400, 300);
            
            // Tạo Trading Panel (sẽ được tạo đơn giản)
            TaoTradingPanel(canvasObj, machine, machineUI);
            
            // Gán canvas vào machine
            SerializedObject so = new SerializedObject(machine);
            so.FindProperty("m_UICanvas").objectReferenceValue = canvas;
            so.ApplyModifiedProperties();
            
            Debug.Log("[TradingMachineSetupWizard] Đã tạo UI system");
        }

        private void TaoTradingPanel(GameObject canvasParent, TradingMachine machine, TradingMachineUI machineUI)
        {
            // Tạo panel chính
            GameObject panel = new GameObject("TradingPanel");
            panel.transform.SetParent(canvasParent.transform, false);
            
            RectTransform panelRect = panel.AddComponent<RectTransform>();
            panelRect.anchorMin = Vector2.zero;
            panelRect.anchorMax = Vector2.one;
            panelRect.offsetMin = Vector2.zero;
            panelRect.offsetMax = Vector2.zero;
            
            Image panelImage = panel.AddComponent<Image>();
            panelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);
            
            // Tạo title text
            GameObject titleObj = new GameObject("Title");
            titleObj.transform.SetParent(panel.transform, false);
            
            TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = m_TenMay;
            titleText.fontSize = 24;
            titleText.color = Color.white;
            titleText.alignment = TextAlignmentOptions.Center;
            
            RectTransform titleRect = titleObj.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.8f);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;
            
            // Gán references cơ bản
            SerializedObject so = new SerializedObject(machine);
            so.FindProperty("m_PanelGiaoDich").objectReferenceValue = panel;
            so.FindProperty("m_TextTenMay").objectReferenceValue = titleText;
            so.ApplyModifiedProperties();
            
            // Gán vào UI component
            SerializedObject uiSo = new SerializedObject(machineUI);
            uiSo.FindProperty("m_PanelGiaoDich").objectReferenceValue = panel;
            uiSo.FindProperty("m_TextTenMay").objectReferenceValue = titleText;
            uiSo.ApplyModifiedProperties();
        }

        private void TaoEffects(GameObject parent, TradingMachine machine)
        {
            // Tạo AudioSource
            AudioSource audioSource = parent.AddComponent<AudioSource>();
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 1f; // 3D sound
            
            // Gán vào machine
            SerializedObject so = new SerializedObject(machine);
            so.FindProperty("m_AudioSource").objectReferenceValue = audioSource;
            so.ApplyModifiedProperties();
            
            Debug.Log("[TradingMachineSetupWizard] Đã tạo effects");
        }

        private void ThietLapTriggerZone(GameObject parent, TradingZoneDetector detector)
        {
            // Thêm BoxCollider làm trigger
            BoxCollider triggerCol = parent.AddComponent<BoxCollider>();
            triggerCol.isTrigger = true;
            triggerCol.size = new Vector3(6f, 4f, 6f);
            triggerCol.center = new Vector3(0, 2f, 0);
            
            // Cấu hình detector
            SerializedObject so = new SerializedObject(detector);
            so.FindProperty("m_TriggerZone").objectReferenceValue = triggerCol;
            so.FindProperty("m_KhoangCachTuongTac").floatValue = 3f;
            so.FindProperty("m_HienThiLog").boolValue = true;
            so.FindProperty("m_HienThiGizmos").boolValue = true;
            so.ApplyModifiedProperties();
            
            Debug.Log("[TradingMachineSetupWizard] Đã thiết lập trigger zone");
        }

        private void MoHuongDan()
        {
            string path = "Assets/Script/Economy/HUONG_DAN_CAI_DAT_LEA_CURRENCY_SYSTEM.md";
            UnityEditorInternal.InternalEditorUtility.OpenFileAtLineExternal(path, 630); // Mở tại phần Trading Machine
        }

        private void KiemTraEconomySystem()
        {
            EconomySystemManager economy = FindObjectOfType<EconomySystemManager>();
            string report = "=== KIỂM TRA ECONOMY SYSTEM ===\n\n";
            
            if (economy != null)
            {
                report += "✅ EconomySystemManager: Có\n";
                report += $"✅ Trạng thái: {(economy.HeThongSanSang ? "Sẵn sàng" : "Chưa sẵn sàng")}\n";
                
                if (economy.CurrencyManager != null)
                    report += $"✅ Currency: {economy.CurrencyManager.DinhDangTienHienTai()}\n";
                
                if (economy.PlayerInventory != null)
                    report += $"✅ Inventory: {economy.PlayerInventory.SoSlotDaDung} slots đã dùng\n";
            }
            else
            {
                report += "❌ EconomySystemManager: Không tìm thấy!\n";
                report += "Hãy chạy Economy Setup Wizard trước.";
            }
            
            EditorUtility.DisplayDialog("Báo Cáo Economy System", report, "OK");
        }

        private void TimTradingMachines()
        {
            TradingMachine[] machines = FindObjectsOfType<TradingMachine>();
            string report = $"=== TRADING MACHINES ({machines.Length}) ===\n\n";
            
            if (machines.Length > 0)
            {
                foreach (var machine in machines)
                {
                    report += $"🏪 {machine.name}\n";
                    // Có thể thêm thông tin chi tiết ở đây
                }
            }
            else
            {
                report += "Không tìm thấy Trading Machine nào.";
            }
            
            EditorUtility.DisplayDialog("Trading Machines", report, "OK");
            
            if (machines.Length > 0)
                Selection.objects = System.Array.ConvertAll(machines, m => m.gameObject);
        }
        #endregion
    }
}
